<?xml version="1.0" encoding="UTF-8"?>
<EmailServicesFunction xmlns="http://soap.sforce.com/2006/04/metadata">
    <apexClass>MCSDependencyEmailService</apexClass>
    <attachmentOption>None</attachmentOption>
    <authenticationFailureAction>Discard</authenticationFailureAction>
    <authorizationFailureAction>Discard</authorizationFailureAction>
    <emailServicesAddresses>
        <developerName>MCS_Dependency_Email</developerName>
        <isActive>true</isActive>
        <localPart>mcs_dependency_email_service</localPart>
        <runAsUser><EMAIL></runAsUser>
    </emailServicesAddresses>
    <functionInactiveAction>Discard</functionInactiveAction>
    <functionName>MCS Dependency Email Service</functionName>
    <isActive>true</isActive>
    <isAuthenticationRequired>false</isAuthenticationRequired>
    <isErrorRoutingEnabled>false</isErrorRoutingEnabled>
    <isTextAttachmentsAsBinary>false</isTextAttachmentsAsBinary>
    <isTlsRequired>false</isTlsRequired>
    <overLimitAction>Discard</overLimitAction>
</EmailServicesFunction>
