/**
 * Test class for SubscriptionTriggerExecutionController
 * 
 * <AUTHOR> Agent
 * @date 2024
 * @description Comprehensive test coverage for SubscriptionTriggerExecutionController class
 */
@IsTest
private class SubscriptionTriggerExecutionControllerTest {
    
    private static User sysAdmin;
    
    @TestSetup
    static void makeData() {
        // Get system admin user
        sysAdmin = [SELECT Id FROM User WHERE Profile.Name = 'System Administrator' AND IsActive = TRUE LIMIT 1];
        
        System.runAs(sysAdmin) {
            // Create test account
            Account testAccount = new Account(
                Name = 'Test Subscription Account'
            );
            insert testAccount;
            
            // Create test contact
            Contact testContact = new Contact(
                FirstName = 'Test',
                LastName = 'Contact',
                Email = '<EMAIL>',
                AccountId = testAccount.Id
            );
            insert testContact;
            
            // Create test opportunity
            Opportunity testOpp = new Opportunity(
                Name = 'Test Subscription Opportunity',
                AccountId = testAccount.Id,
                StageName = 'S1 - Recognize Needs',
                CloseDate = Date.today().addDays(30)
            );
            insert testOpp;
            
            // Create test product
            Product2 testProduct = new Product2(
                Name = 'Test AI Product',
                ProductCode = '16091',
                IsActive = true
            );
            insert testProduct;
            
            // Create standard pricebook entry
            PricebookEntry standardPBE = new PricebookEntry(
                Pricebook2Id = Test.getStandardPricebookId(),
                Product2Id = testProduct.Id,
                UnitPrice = 100,
                IsActive = true
            );
            insert standardPBE;
            
            // Create test quote
            SBQQ__Quote__c testQuote = new SBQQ__Quote__c(
                SBQQ__Account__c = testAccount.Id,
                SBQQ__Opportunity2__c = testOpp.Id,
                SBQQ__Primary__c = true,
                SBQQ__Type__c = 'Quote',
                SBCF_Ship_to_Contact__c = testContact.Id,
                SBCF_Ship_To_Contact_Email__c = testContact.Email
            );
            insert testQuote;
            
            // Create test quote line
            SBQQ__QuoteLine__c testQuoteLine = new SBQQ__QuoteLine__c(
                SBQQ__Quote__c = testQuote.Id,
                SBQQ__Product__c = testProduct.Id,
                SBQQ__Quantity__c = 10,
                SBQQ__ListPrice__c = 100,
                SBQQ__NetPrice__c = 100,
                SBQQ__EndDate__c = Date.today().addDays(365)
            );
            insert testQuoteLine;
            
            // Create test contract
            Contract testContract = new Contract(
                AccountId = testAccount.Id,
                SBQQ__Quote__c = testQuote.Id,
                SBQQ__Opportunity__c = testOpp.Id,
                StartDate = Date.today(),
                ContractTerm = 12
            );
            insert testContract;
            
            // Create test DSI
            DSI__c testDSI = new DSI__c(
                Name = 'Test DSI',
                Account__c = testAccount.Id,
                AI__c = false,
                AI_Status__c = 'Inactive'
            );
            insert testDSI;
            
            // Create test entitlement
            Entitlement testEntitlement = new Entitlement(
                Name = 'Test Entitlement',
                AccountId = testAccount.Id,
                StartDate = Date.today(),
                EndDate = Date.today().addDays(365)
            );
            insert testEntitlement;
        }
    }
    
    /**
     * Test TriggerHandler interface methods
     */
    @IsTest
    static void testTriggerHandlerInterface() {
        System.runAs(sysAdmin) {
            SubscriptionTriggerExecutionController controller = new SubscriptionTriggerExecutionController();
            
            Test.startTest();
            
            // Test interface methods
            System.assertEquals(0, controller.getRecursionDepth(), 'Recursion depth should be 0');
            System.assertEquals(0, controller.getMaxRecursionDepthAllowed(), 'Max recursion depth should be 0');
            System.assertEquals(true, controller.isEnabled(), 'Controller should be enabled');
            
            // Test bulk methods
            controller.bulkBefore();
            controller.bulkAfter();
            
            // Test empty trigger methods
            controller.beforeDelete(null);
            controller.afterDelete(null);
            controller.afterUndelete(null);
            
            Test.stopTest();
        }
    }
    
    /**
     * Test beforeInsert method with various subscription scenarios
     */
    @IsTest
    static void testBeforeInsert() {
        System.runAs(sysAdmin) {
            // Get test data
            SBQQ__QuoteLine__c quoteLine = [SELECT Id FROM SBQQ__QuoteLine__c LIMIT 1];
            Contract testContract = [SELECT Id FROM Contract LIMIT 1];
            
            Test.startTest();
            
            // Create subscription with original quote line
            SBQQ__Subscription__c sub1 = new SBQQ__Subscription__c(
                SBQQ__OriginalQuoteLine__c = quoteLine.Id,
                SBQQ__Contract__c = testContract.Id,
                SBQQ__Quantity__c = 5
            );
            
            // Create subscription with education coupon code
            SBQQ__Subscription__c sub2 = new SBQQ__Subscription__c(
                Edu_CouponCode__c = 'ANL-TEST-123',
                SBQQ__Contract__c = testContract.Id,
                SBQQ__Quantity__c = 3
            );
            
            // Create subscription with key group
            SBQQ__Subscription__c sub3 = new SBQQ__Subscription__c(
                Quote_Line_Group__c = 1,
                SBQQ__ProductName__c = 'Test Product',
                SBQQ__Contract__c = testContract.Id,
                SBQQ__Quantity__c = 2
            );
            
            // Create AI subscription
            SBQQ__Subscription__c sub4 = new SBQQ__Subscription__c(
                Product_Code__c = '16091',
                SBQQ__Quantity__c = 10,
                SBQQ__Contract__c = testContract.Id
            );
            
            insert new List<SBQQ__Subscription__c>{sub1, sub2, sub3, sub4};
            
            Test.stopTest();
            
            // Verify AI subscription was updated
            SBQQ__Subscription__c updatedSub4 = [SELECT AI__c, AI_Questions_Entitlement__c FROM SBQQ__Subscription__c WHERE Id = :sub4.Id];
            System.assertEquals(true, updatedSub4.AI__c, 'AI flag should be set to true');
            System.assertEquals(200000, updatedSub4.AI_Questions_Entitlement__c, 'AI questions entitlement should be calculated correctly');
            
            // Verify notification trigger date was set for education subscription
            SBQQ__Subscription__c updatedSub2 = [SELECT Notification_Trigger_Date_dtm__c FROM SBQQ__Subscription__c WHERE Id = :sub2.Id];
            System.assertNotEquals(null, updatedSub2.Notification_Trigger_Date_dtm__c, 'Notification trigger date should be set');
        }
    }
    
    /**
     * Test afterInsert method
     */
    @IsTest
    static void testAfterInsert() {
        System.runAs(sysAdmin) {
            // Get test data
            SBQQ__QuoteLine__c quoteLine = [SELECT Id FROM SBQQ__QuoteLine__c LIMIT 1];
            Contract testContract = [SELECT Id FROM Contract LIMIT 1];
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            DSI__c testDSI = [SELECT Id FROM DSI__c LIMIT 1];
            
            Test.startTest();
            
            // Create subscription with education coupon
            SBQQ__Subscription__c sub1 = new SBQQ__Subscription__c(
                Edu_CouponCode__c = 'ARC-TEST-456',
                SBQQ__QuoteLine__c = quoteLine.Id,
                SBQQ__Contract__c = testContract.Id,
                SBQQ__Account__c = testAccount.Id,
                SBQQ__StartDate__c = Date.today(),
                SBCF_DSI__c = testDSI.Id,
                AI__c = true,
                Product_Code__c = '16091',
                SBQQ__EndDate__c = Date.today().addDays(365),
                Depleted_date__c = null,
                SBQQ__Quantity__c = 5
            );
            
            insert sub1;
            
            Test.stopTest();
            
            // Verify DSI was updated for AI
            DSI__c updatedDSI = [SELECT AI__c, AI_Status__c FROM DSI__c WHERE Id = :testDSI.Id];
            System.assertEquals(true, updatedDSI.AI__c, 'DSI AI flag should be set to true');
            System.assertEquals('Active', updatedDSI.AI_Status__c, 'DSI AI status should be Active');
        }
    }

    /**
     * Test beforeUpdate method
     */
    @IsTest
    static void testBeforeUpdate() {
        System.runAs(sysAdmin) {
            // Get test data
            SBQQ__QuoteLine__c quoteLine = [SELECT Id FROM SBQQ__QuoteLine__c LIMIT 1];
            Contract testContract = [SELECT Id FROM Contract LIMIT 1];

            // Create subscription
            SBQQ__Subscription__c sub = new SBQQ__Subscription__c(
                SBQQ__OriginalQuoteLine__c = quoteLine.Id,
                SBQQ__Contract__c = testContract.Id,
                AI_Questions_Entitlement__c = 1000,
                AI_questions_used__c = 500,
                SBQQ__Quantity__c = 1
            );
            insert sub;

            Test.startTest();

            // Update subscription to deplete AI questions
            sub.AI_questions_used__c = 1000;
            update sub;

            Test.stopTest();

            // Verify depleted date was set
            SBQQ__Subscription__c updatedSub = [SELECT Depleted_date__c FROM SBQQ__Subscription__c WHERE Id = :sub.Id];
            System.assertEquals(Date.today(), updatedSub.Depleted_date__c, 'Depleted date should be set to today');
        }
    }

    /**
     * Test afterUpdate method
     */
    @IsTest
    static void testAfterUpdate() {
        System.runAs(sysAdmin) {
            // Get test data
            Contract testContract = [SELECT Id FROM Contract LIMIT 1];
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            DSI__c testDSI = [SELECT Id FROM DSI__c LIMIT 1];

            // Create subscription
            SBQQ__Subscription__c sub = new SBQQ__Subscription__c(
                SBQQ__Contract__c = testContract.Id,
                SBQQ__Account__c = testAccount.Id,
                SBCF_DSI__c = testDSI.Id,
                SBQQ__StartDate__c = Date.today(),
                SBQQ__EndDate__c = Date.today().addDays(365),
                SBQQ__SubscriptionStartDate__c = Date.today(),
                SBQQ__SubscriptionEndDate__c = Date.today().addDays(365),
                KeyShipped__c = false,
                AI_questions_used__c = 100,
                Product_Code__c = '16091',
                Depleted_date__c = null,
                SBQQ__Quantity__c = 1
            );
            insert sub;

            Test.startTest();

            // Update subscription to trigger various scenarios
            sub.KeyShipped__c = true;
            sub.SBQQ__SubscriptionEndDate__c = Date.today().addDays(400);
            sub.SBQQ__TerminatedDate__c = Date.today();
            sub.AI_questions_used__c = 200;
            sub.Depleted_date__c = Date.today().addDays(-1);
            update sub;

            Test.stopTest();

            // Verify subscription was processed
            System.assertNotEquals(null, sub.Id, 'Subscription should exist after update');
        }
    }

    /**
     * Test andFinally method with various scenarios
     */
    @IsTest
    static void testAndFinallyMethod() {
        System.runAs(sysAdmin) {
            // Get test data
            SBQQ__QuoteLine__c quoteLine = [SELECT Id FROM SBQQ__QuoteLine__c LIMIT 1];
            Contract testContract = [SELECT Id FROM Contract LIMIT 1];
            Account testAccount = [SELECT Id FROM Account LIMIT 1];
            DSI__c testDSI = [SELECT Id FROM DSI__c LIMIT 1];

            Test.startTest();

            // Create subscription that will trigger andFinally logic
            SBQQ__Subscription__c sub = new SBQQ__Subscription__c(
                SBQQ__QuoteLine__c = quoteLine.Id,
                SBQQ__Contract__c = testContract.Id,
                SBQQ__Account__c = testAccount.Id,
                SBCF_DSI__c = testDSI.Id,
                SBQQ__StartDate__c = Date.today(),
                SBQQ__EndDate__c = Date.today().addDays(365),
                Edu_CouponCode__c = 'ANL-FINAL-TEST',
                AI__c = true,
                Product_Code__c = '16091',
                SBQQ__Quantity__c = 1
            );
            insert sub;

            Test.stopTest();

            // Verify subscription was created successfully
            SBQQ__Subscription__c createdSub = [SELECT Id, Edu_CouponCode__c FROM SBQQ__Subscription__c WHERE Id = :sub.Id];
            System.assertEquals('ANL-FINAL-TEST', createdSub.Edu_CouponCode__c, 'Education coupon code should be preserved');
        }
    }

    /**
     * Test getDSIAggregateMap method
     */
    @IsTest
    static void testGetDSIAggregateMap() {
        System.runAs(sysAdmin) {
            SubscriptionTriggerExecutionController controller = new SubscriptionTriggerExecutionController();

            // Get test data
            DSI__c testDSI = [SELECT Id FROM DSI__c LIMIT 1];

            // Create test subscription
            SBQQ__Subscription__c sub = new SBQQ__Subscription__c(
                SBCF_DSI__c = testDSI.Id,
                SBQQ__EndDate__c = Date.today().addDays(365),
                Depleted_date__c = Date.today().addDays(30),
                Remaining_AI_Questions__c = 1000,
                SBQQ__Quantity__c = 1
            );

            Map<Id, Double> testMap = new Map<Id, Double>();

            Test.startTest();

            // Test method - should return null in test context
            Map<Id, Double> result = controller.getDSIAggregateMap(sub, testMap);

            Test.stopTest();

            // Verify result is null in test context
            System.assertEquals(null, result, 'Method should return null in test context');
        }
    }

    /**
     * Test with AI SKUs and account updates
     */
    @IsTest
    static void testAISKUsAndAccountUpdates() {
        System.runAs(sysAdmin) {
            // Get test data
            Contract testContract = [SELECT Id FROM Contract LIMIT 1];
            Account testAccount = [SELECT Id FROM Account LIMIT 1];

            Test.startTest();

            // Create subscriptions with different AI SKUs
            List<SBQQ__Subscription__c> subs = new List<SBQQ__Subscription__c>();

            for (String sku : new List<String>{'16091', '16094', '89408', '89406', '89407'}) {
                SBQQ__Subscription__c sub = new SBQQ__Subscription__c(
                    SBQQ__Contract__c = testContract.Id,
                    SBQQ__Account__c = testAccount.Id,
                    Product_Code__c = sku,
                    SBQQ__EndDate__c = Date.today().addDays(365),
                    Depleted_date__c = null,
                    SBQQ__Quantity__c = 1
                );
                subs.add(sub);
            }

            insert subs;

            Test.stopTest();

            // Verify subscriptions were created
            List<SBQQ__Subscription__c> createdSubs = [SELECT Id, Product_Code__c FROM SBQQ__Subscription__c WHERE Id IN :subs];
            System.assertEquals(5, createdSubs.size(), 'All AI SKU subscriptions should be created');
        }
    }
}
