/**
 * Test class for MCSTrialRequestPayload
 * 
 * <AUTHOR> Agent
 * @date 2024
 * @description Comprehensive test coverage for MCSTrialRequestPayload class and its inner classes
 */
@IsTest
private class MCSTrialRequestPayloadTest {
    
    /**
     * Test the default constructor with basic parameters
     */
    @IsTest
    static void testDefaultConstructor() {
        // Test data
        String testEmail = '<EMAIL>';
        String testFirstName = 'John';
        String testLastName = 'Doe';
        DateTime testStartDate = DateTime.newInstance(2024, 1, 1, 0, 0, 0);
        DateTime testEndDate = DateTime.newInstance(2024, 12, 31, 23, 59, 59);
        
        Test.startTest();
        
        // Create payload using default constructor
        MCSTrialRequestPayload payload = new MCSTrialRequestPayload(
            testEmail, 
            testFirstName, 
            testLastName, 
            testStartDate, 
            testEndDate
        );
        
        Test.stopTest();
        
        // Verify main payload properties
        System.assertEquals('us-east-1', payload.region, 'Default region should be us-east-1');
        System.assertEquals('MCS', payload.environmentType, 'Environment type should be MCS');
        System.assertEquals('trial', payload.plan, 'Default plan should be trial');
        System.assertNotEquals(null, payload.numberOfUsers, 'Number of users should not be null');
        System.assert(payload.numberOfUsers > 0, 'Number of users should be positive');
        
        // Verify user details
        System.assertNotEquals(null, payload.user, 'User details should not be null');
        System.assertEquals(testEmail, payload.user.email, 'User email should match');
        System.assertEquals(testFirstName, payload.user.firstName, 'User first name should match');
        System.assertEquals(testLastName, payload.user.lastName, 'User last name should match');
        
        // Verify subscription details
        System.assertNotEquals(null, payload.subscription, 'Subscription details should not be null');
        System.assertEquals(
            testStartDate.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''), 
            payload.subscription.activationDate, 
            'Activation date should be formatted correctly'
        );
        System.assertEquals(
            testEndDate.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''), 
            payload.subscription.expirationDate, 
            'Expiration date should be formatted correctly'
        );
    }
    
    /**
     * Test the extended constructor with custom parameters
     */
    @IsTest
    static void testExtendedConstructor() {
        // Test data
        String testEmail = '<EMAIL>';
        String testFirstName = 'Jane';
        String testLastName = 'Smith';
        DateTime testStartDate = DateTime.newInstance(2024, 6, 15, 12, 30, 45);
        DateTime testEndDate = DateTime.newInstance(2024, 12, 15, 18, 45, 30);
        String testPlan = 'premium';
        String testRegion = 'eu-west-1';
        Integer testNumberOfUsers = 100;
        
        Test.startTest();
        
        // Create payload using extended constructor
        MCSTrialRequestPayload payload = new MCSTrialRequestPayload(
            testEmail, 
            testFirstName, 
            testLastName, 
            testStartDate, 
            testEndDate,
            testPlan,
            testRegion,
            testNumberOfUsers
        );
        
        Test.stopTest();
        
        // Verify main payload properties
        System.assertEquals(testRegion, payload.region, 'Region should match custom value');
        System.assertEquals('MCS', payload.environmentType, 'Environment type should be MCS');
        System.assertEquals(testPlan, payload.plan, 'Plan should match custom value');
        System.assertEquals(testNumberOfUsers, payload.numberOfUsers, 'Number of users should match custom value');
        
        // Verify user details
        System.assertNotEquals(null, payload.user, 'User details should not be null');
        System.assertEquals(testEmail, payload.user.email, 'User email should match');
        System.assertEquals(testFirstName, payload.user.firstName, 'User first name should match');
        System.assertEquals(testLastName, payload.user.lastName, 'User last name should match');
        
        // Verify subscription details
        System.assertNotEquals(null, payload.subscription, 'Subscription details should not be null');
        System.assertEquals(
            testStartDate.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''), 
            payload.subscription.activationDate, 
            'Activation date should be formatted correctly'
        );
        System.assertEquals(
            testEndDate.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\''), 
            payload.subscription.expirationDate, 
            'Expiration date should be formatted correctly'
        );
    }
    
    /**
     * Test UserDetails inner class constructor
     */
    @IsTest
    static void testUserDetailsConstructor() {
        // Test data
        String testEmail = '<EMAIL>';
        String testFirstName = 'Test';
        String testLastName = 'User';
        
        Test.startTest();
        
        // Create UserDetails instance
        MCSTrialRequestPayload.UserDetails userDetails = new MCSTrialRequestPayload.UserDetails(
            testEmail, 
            testFirstName, 
            testLastName
        );
        
        Test.stopTest();
        
        // Verify all properties
        System.assertEquals(testEmail, userDetails.email, 'Email should match');
        System.assertEquals(testFirstName, userDetails.firstName, 'First name should match');
        System.assertEquals(testLastName, userDetails.lastName, 'Last name should match');
    }
    
    /**
     * Test SubscriptionDetails inner class constructor
     */
    @IsTest
    static void testSubscriptionDetailsConstructor() {
        // Test data
        String testActivationDate = '2024-01-01T00:00:00Z';
        String testExpirationDate = '2024-12-31T23:59:59Z';
        
        Test.startTest();
        
        // Create SubscriptionDetails instance
        MCSTrialRequestPayload.SubscriptionDetails subscriptionDetails = new MCSTrialRequestPayload.SubscriptionDetails(
            testActivationDate, 
            testExpirationDate
        );
        
        Test.stopTest();
        
        // Verify all properties
        System.assertEquals(testActivationDate, subscriptionDetails.activationDate, 'Activation date should match');
        System.assertEquals(testExpirationDate, subscriptionDetails.expirationDate, 'Expiration date should match');
    }
    
    /**
     * Test with edge case values
     */
    @IsTest
    static void testEdgeCaseValues() {
        // Test data with edge cases
        String testEmail = '';
        String testFirstName = null;
        String testLastName = 'SingleName';
        DateTime testStartDate = DateTime.now();
        DateTime testEndDate = DateTime.now().addDays(1);
        String testPlan = '';
        String testRegion = null;
        Integer testNumberOfUsers = 0;
        
        Test.startTest();
        
        // Create payload with edge case values
        MCSTrialRequestPayload payload = new MCSTrialRequestPayload(
            testEmail, 
            testFirstName, 
            testLastName, 
            testStartDate, 
            testEndDate,
            testPlan,
            testRegion,
            testNumberOfUsers
        );
        
        Test.stopTest();
        
        // Verify the payload handles edge cases
        System.assertEquals(testRegion, payload.region, 'Region should handle null value');
        System.assertEquals('MCS', payload.environmentType, 'Environment type should always be MCS');
        System.assertEquals(testPlan, payload.plan, 'Plan should handle empty string');
        System.assertEquals(testNumberOfUsers, payload.numberOfUsers, 'Number of users should handle zero');
        
        // Verify user details handle edge cases
        System.assertNotEquals(null, payload.user, 'User details should not be null');
        System.assertEquals(testEmail, payload.user.email, 'User email should handle empty string');
        System.assertEquals(testFirstName, payload.user.firstName, 'User first name should handle null');
        System.assertEquals(testLastName, payload.user.lastName, 'User last name should match');
    }
    
    /**
     * Test date formatting with various DateTime values
     */
    @IsTest
    static void testDateFormatting() {
        // Test data with specific date/time values
        DateTime testStartDate = DateTime.newInstanceGmt(2024, 3, 15, 14, 30, 45);
        DateTime testEndDate = DateTime.newInstanceGmt(2024, 9, 22, 9, 15, 30);
        
        Test.startTest();
        
        // Create payload
        MCSTrialRequestPayload payload = new MCSTrialRequestPayload(
            '<EMAIL>', 
            'Test', 
            'User', 
            testStartDate, 
            testEndDate
        );
        
        Test.stopTest();
        
        // Verify date formatting
        String expectedStartDate = '2024-03-15T14:30:45Z';
        String expectedEndDate = '2024-09-22T09:15:30Z';
        
        System.assertEquals(expectedStartDate, payload.subscription.activationDate, 'Start date should be formatted correctly');
        System.assertEquals(expectedEndDate, payload.subscription.expirationDate, 'End date should be formatted correctly');
    }
    
    /**
     * Test JSON serialization capability
     */
    @IsTest
    static void testJSONSerialization() {
        // Test data
        String testEmail = '<EMAIL>';
        String testFirstName = 'JSON';
        String testLastName = 'Test';
        DateTime testStartDate = DateTime.newInstance(2024, 1, 1, 0, 0, 0);
        DateTime testEndDate = DateTime.newInstance(2024, 12, 31, 23, 59, 59);
        
        Test.startTest();
        
        // Create payload
        MCSTrialRequestPayload payload = new MCSTrialRequestPayload(
            testEmail, 
            testFirstName, 
            testLastName, 
            testStartDate, 
            testEndDate
        );
        
        // Test JSON serialization
        String jsonString = JSON.serialize(payload);
        
        Test.stopTest();
        
        // Verify JSON contains expected values
        System.assert(jsonString.contains(testEmail), 'JSON should contain email');
        System.assert(jsonString.contains(testFirstName), 'JSON should contain first name');
        System.assert(jsonString.contains(testLastName), 'JSON should contain last name');
        System.assert(jsonString.contains('us-east-1'), 'JSON should contain region');
        System.assert(jsonString.contains('MCS'), 'JSON should contain environment type');
        System.assert(jsonString.contains('trial'), 'JSON should contain plan');
        System.assert(jsonString.contains('numberOfUsers'), 'JSON should contain numberOfUsers field');
        
        // Verify JSON structure is valid
        System.assertNotEquals(null, jsonString, 'JSON string should not be null');
        System.assert(jsonString.length() > 0, 'JSON string should not be empty');
    }
}
