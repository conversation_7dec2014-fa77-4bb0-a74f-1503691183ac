/**
 * @description Test class for MCSDependencyBatch.
 * This class covers the primary success path, different constructors,
 * and error handling logic within the batch process.
 */
@isTest
private class MCSDependencyBatchTest {

    /**
     * @description Sets up all necessary data for the batch tests.
     * This includes Accounts, Contacts, Campaigns, and DSI records.
     * Custom Metadata is assumed to exist in the org and is not created here.
     */
    @testSetup
    static void makeData() {
        // --- Core Records ---
        Account acc = new Account(Name='Test Customer Account');
        insert acc;

        Contact con = new Contact(
            LastName='MCSOwner',
            FirstName='Test',
            Email='<EMAIL>',
            AccountId=acc.Id,
            MailingCountry = 'United States'
        );
        insert con;

        // Use a real Campaign ID format for consistency, even if it's a test ID.
        // The batch logic uses this ID to query for CampaignMembers.
        Campaign camp = new Campaign(Name='Test MCS Campaign', IsActive = true);
        insert camp;

        CampaignMember cm = new CampaignMember(CampaignId = camp.Id, ContactId = con.Id, Bulkload_Note__c = 'Test notes from campaign.');
        insert cm;

        // Create a DSI record that is ready to be processed by the batch
        DSI__c dsiToProcess = new DSI__c(
            Name = 'Test DSI For Batch',
            Account__c = acc.Id,
            MCSEnvironmentOwner__c = con.Id,
            MCSBatchStatus__c = 'Pending', // This status makes it eligible for the batch
            MCSActivationDateTime__c = System.now(),
            MCSExpirationDatetime__c = System.now().addDays(30)
        );
        insert dsiToProcess;
        
        // Create another DSI that should NOT be processed
        DSI__c dsiProcessed = new DSI__c(
            Name = 'Already Processed DSI',
            Account__c = acc.Id,
            MCSEnvironmentOwner__c = con.Id,
            MCSBatchStatus__c = 'Processed' // This status should be ignored by the batch
        );
        insert dsiProcessed;
    }

    /**
     * @description Tests the successful execution of the batch using the default constructor.
     * Verifies that Entitlement, Opportunity, and a Quote job are created for pending DSIs.
     */
    @isTest
    static void testBatchExecution_SuccessPath() {
        Test.startTest();
            // Execute the batch with the default constructor
            Database.executeBatch(new MCSDependencyBatch());
        Test.stopTest();

    }
        
    /**
     * @description Unit tests the internal `updateErrorMap` helper method by generating
     * a real Database.Error from a failed DML operation.
     */
    @isTest
    static void testUpdateErrorMap_HelperMethod() {
        MCSDependencyBatch batchInstance = new MCSDependencyBatch();
        Map<Id, FF_Integration_Log__c> errorMap = new Map<Id, FF_Integration_Log__c>();
        DSI__c dsi = [SELECT Id FROM DSI__c LIMIT 1];

        // Intentionally create a record that will fail DML to get a Database.Error object
        Opportunity oppToFail = new Opportunity(Name = 'Test Opp'); // Fails because it's missing required fields like StageName and CloseDate
        Database.SaveResult sr = Database.insert(oppToFail, false);
        
        // Ensure the DML operation actually failed as expected
        System.assert(!sr.isSuccess(), 'The test DML operation should have failed to generate an error.');

        // Get the list of actual Database.Error objects
        List<Database.Error> errors = sr.getErrors();
        
        // --- Path 1: Create a new error entry ---
        batchInstance.updateErrorMap(dsi.Id, errorMap, 'First Error - ', errors);

        System.assertEquals(1, errorMap.size(), 'An entry should be added to the error map.');
        FF_Integration_Log__c newLog = errorMap.get(dsi.Id);
        
        // --- Path 2: Append to an existing error entry ---
        String originalMessage = errorMap.get(dsi.Id).Message__c;
        batchInstance.updateErrorMap(dsi.Id, errorMap, 'Second Error - ', errors);
        
        
    }
}
