/************************************* MODIFICATION LOG ********************************************************************************************
* Campaign Test class
*
* DESCRIPTION : Updating the IsMaxCampaignDateof campaign members.
*             : Updating the lead Original campaign source
*             : Rollup triggers to show Count Lead, Count Opp, Sum of pipe,fsct and act for Rollup related to original Campaign source(LeadTriggers,LeadTriggerHelper)
*             : Autopopulate district(leadTriggerhelper) , Autopopulate Account(leadTriggerhelper)
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                  REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Amritha Buddharaju           11/04/2015          - Original Version
* Amritha Buddharaju           12/28/2015         - added lead original campaign source logic
* Amritha Buddharaju           10/10/2016         - added lead original campaign source Rollup(leadTriggerhelper)
* Amritha Buddharaju           10/28/2016         - added Autopopulate district(leadTriggerhelper) 
* Amritha Buddharaju           11/16/2016         - added Autopopulate Account(leadTriggerhelper)   
* Himanshu Kalra               11/01/2017         - covered LeadMostRecentActionBatch and Class 'CampaignTriggerHandler' Method:updateMostRecentAction for Case Number - 209904
* Alla Kiyashko                05/21/2018         - added updateCMEngagementScore  to test updateCMEngagementScore method of CammpaignTriggerHandler
Test class covers 8 classes and 2 triggers
Classes:
LeadOriginalCampaignSourceBatch
LeadMostRecentActionBatch
LeadCountRollUpBatch
CampaignTriggerHandler
leadTriggerHelper
PopulateDistrictOnLeadBatch
PopulateAccountOnLeadBatch
LeadCountRollUpBatch
updateCMEngagementScore 
Trigger:
CampaignMemberTrigger
LeadTriggers 
*/

@IsTest
public class CampaignTriggerTest {

    @IsTest
    static void calculateParentCampaignTest() {
        Campaign c1 = new Campaign(
            Name = 'Sales Line 1',
            IsActive = true,
            RecordTypeId = TriggersHelper.parentCampaignRecordTypeId,
            Recalculate__c = true,
            Type = '2. DM-Email'
        );
        insert c1;
        Campaign c2 = new Campaign(
            Name = 'Sales Line 2',
            IsActive = true,
            RecordTypeId = TriggersHelper.campaignOfferRecordTypeId,
            ParentId = c1.Id
        );
        insert c2;
    }
    @IsTest
    static void createDuplicateNameCampaignTest() {
        Campaign c1 = new Campaign(
            Name = 'Sales Line',
            IsActive = true,
            RecordTypeId = TriggersHelper.campaignOfferRecordTypeId,
            Type = '2. DM-Email'
        );

        Campaign c2 = new Campaign(
            Name = 'Sales Line',
            IsActive = true,
            RecordTypeId = TriggersHelper.campaignOfferRecordTypeId,
            ParentId = c1.Id
        );
        try {
            insert new List<Campaign> {
                c1, c2
            };
        } catch (Exception e) {
            System.assert(true);
        }
    }
    @IsTest
    static void createCampaignTest() {
        Campaign c1 = new Campaign(Name = 'Sales Line', IsActive = true, RecordTypeId = TriggersHelper.campaignOfferRecordTypeId);
        c1.Type = '2. DM-Email';

        Campaign c2 = new Campaign(Name = 'Sales Line1', IsActive = true, RecordTypeId = TriggersHelper.campaignOfferRecordTypeId, ParentId = c1.Id);

        Campaign c3 = new Campaign(Name = 'Sales Line2', IsActive = true, RecordTypeId = TriggersHelper.campaignChannelRecordTypeId);
        insert new List<Campaign> {
            c1, c2, c3
        };

        Lead L1 = new Lead(LastName = 'Test', FirstName = 'Lead1', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'US', Status = 'L2 - Contacted');
        insert L1;

        CampaignMember cm1 = new CampaignMember(Status = 'Responded', LeadId = L1.Id, CampaignId = c1.Id);

        CampaignMember cm2 = new CampaignMember(Status = 'Responded', LeadId = L1.Id, CampaignId = c2.Id);

        CampaignMember cm3 = new CampaignMember(Status = 'Responded', LeadId = L1.Id, CampaignId = c3.Id);
        
        insert new List<CampaignMember> {
            cm1, cm2, cm3
        };
        System.runAs(TriggersHelper.systemAdminUser) {
            // ------------Count Lead, Count Opp, Sum of pipe,fsct and act for Rollup related to original Campaign source----------

            Account acc = new Account(Name = 'Test2  Account', BillingCountry = 'United States', BillingCity = 'Test City',
                BillingPostalCode = '12345', BillingState = 'VA', BillingStreet = 'Test Street');
            insert acc;

            Business_Unit__c bu = new Business_Unit__c(Name = 'Geo-Coding');
            insert bu;

            Territory__c te = new Territory__c(Name = 'Testte', Business_Unit__c = bu.Id);
            insert te;

            District__c ds = new District__c (Name = 'Richmond', Territory__c = te.Id, Business_Unit__c = bu.Id);
            insert ds;

            District_Assignment__c dist = new District_Assignment__c(Name = 'Richmond', District__c = ds.Id, Key__c = 'United States-VA');
            insert dist;

            Account_Domain__c ad = new Account_Domain__c(Account__c = acc.Id, Domain__c = 'ventura.org');
            insert ad;

            Lead L2 = new Lead(LastName = 'Test1', FirstName = 'Lead1', Email = '<EMAIL>', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'United States', State = 'VA', Original_Campaign_Source__c = c3.Id, Status = 'L2 - Contacted');

            Lead L3 = new Lead(LastName = 'Test2', FirstName = 'Lead2', Email = '<EMAIL>', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'United States', State = 'VA', Original_Campaign_Source__c = c3.Id, Status = 'L2 - Contacted');

            Lead L4 = new Lead(LastName = 'Test3', FirstName = 'Lead3', Email = '<EMAIL>', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'United States', State = 'VA', Original_Campaign_Source__c = c3.Id, Status = 'L2 - Contacted');
            insert new List<Lead> {
                L2, L3, L4
            };

            //-----------------Autopopulate District on Lead----------------
            //-----------------Autopopulate Account on Lead----------------
            List<Lead> tempLeads = new List<Lead>();
            tempLeads.add(L3);
            tempLeads.add(L4);
            LeadTriggerHelper.AutopopulateDistrict(tempLeads, true);
            LeadTriggerHelper.AutoPopAccountDomain(tempLeads, true);

            Test.startTest();

            delete L2;

            L3.Original_Campaign_Source__c = c2.Id;
            update L3;

            System.assertEquals(false, [SELECT Id, IsMaxCampaignDate__c FROM CampaignMember WHERE Id = :cm1.Id].IsMaxCampaignDate__c);
            //system.assertEquals(true, [select Id,IsMaxCampaignDate__c from CampaignMember Where Id =: cm2.Id].IsMaxCampaignDate__c);

            System.assertEquals(ds.Id, [SELECT Id, District__c FROM Lead WHERE Id = :L3.Id].District__c);

            PopulateDistrictOnLeadBatch c = new PopulateDistrictOnLeadBatch();
            Id batchprocessid = Database.executeBatch(c);

            PopulateAccountOnLeadBatch d = new PopulateAccountOnLeadBatch();
            Id batchprocessid2 = Database.executeBatch(d);

            LeadCountRollUpBatch e = new LeadCountRollUpBatch();
            Id batchprocessid3 = Database.executeBatch(e);

            Test.stopTest();
        }
    }

    @IsTest
    static void updateMaxCampaignDateBatch() {

        Campaign c1 = new Campaign(Name = 'Sales Line', IsActive = true, RecordTypeId = TriggersHelper.campaignSalesRecordTypeId);
        insert c1;

        Campaign c2 = new Campaign(Name = 'Sales Line1', IsActive = true, RecordTypeId = TriggersHelper.campaignChannelRecordTypeId );
        insert c2;

        Lead L1 = new Lead(LastName = 'Test', FirstName = 'Lead1', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'US', Original_Campaign_Source__c = c2.Id, Status = 'L2 - Contacted');
        insert L1;

        // recordtype != offer
        CampaignMember cm1 = new CampaignMember(Status = 'Sent', LeadId = L1.Id, CampaignId = c1.Id);
        insert cm1;

        CampaignMember cm2 = new CampaignMember(Status = 'Sent', LeadId = L1.Id, CampaignId = c2.Id);
        insert cm2;

        //@Himanshu @Purpose: To cover Class 'CampaignTriggerHandler' Method:updateMostRecentAction -----Start 
        //Refer: Case Number - 209904
        Campaign c3 = new Campaign(Name = 'Sales Line3', IsActive = true, RecordTypeId = TriggersHelper.campaignOfferRecordTypeId);
        insert c3;
        Account acc = new Account(Name = 'Test2  Account', BillingCountry = 'United States', BillingCity = 'Test City',
            BillingPostalCode = '12345', BillingState = 'VA', BillingStreet = 'Test Street');
        insert acc;

        Contact Cont1 = new Contact(LastName = 'Test', FirstName = 'Contact1', AccountId = acc.Id, Email = '<EMAIL>');
        insert Cont1;

        CampaignMember cm3 = new CampaignMember(Status = 'Responded', ContactId = Cont1.Id, CampaignId = c3.Id);
        insert cm3;
        //-----End 
        Test.startTest();

        L1.Original_Campaign_Source__c = null;
        update L1;

        System.enqueueJob(new CampaignMemberMostRecentQueueable(new Set<Id>{Cont1.Id}, new Set<Id>{L1.Id}, false));
        
        Test.stopTest();

    }

    @IsTest
    static void updateCMEngagementScore() {
        System.runAs(TriggersHelper.systemAdminUser) {
            Campaign c1 = new Campaign(Name = 'Sales Line on-demand', Subtype__c = 'Track Show', IsActive = true, RecordTypeId = TriggersHelper.campaignOfferRecordTypeId, StartDate = Date.today(), Post_To_Chatter__c = true, Type = '4. Webinar',
                UserTitleFilter__c = 'All', AccountCategoryFilter__c = '');

            Campaign c2 = new Campaign(Name = 'Sales Line', Type = 'Website', Subtype__c = 'Collateral Download', IsActive = true, RecordTypeId = TriggersHelper.campaignOfferRecordTypeId, StartDate = Date.today());
            insert new List<Campaign> {
                c1, c2
            };

            List<CampaignMemberStatus > lResponses = new List<CampaignMemberStatus >();
            lResponses.add(new CampaignMemberStatus(CampaignId = c1.Id, Label = 'Attended', SortOrder = 3, IsDefault = true, HasResponded = true));
            lResponses.add(new CampaignMemberStatus(CampaignId = c1.Id, Label = 'Registered', SortOrder = 4, IsDefault = false, HasResponded = true));
            lResponses.add(new CampaignMemberStatus(CampaignId = c2.Id, Label = 'Downloaded', SortOrder = 5, IsDefault = false, HasResponded = true));
            insert lResponses;

            Lead L1 = new Lead(LastName = 'FirstTest', FirstName = 'Lead1', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'US', Status = 'L2 - Contacted');
            insert L1;

            Account acc = new Account(Name = 'Test2  Account', BillingCountry = 'United States', BillingCity = 'Test City',
                BillingPostalCode = '12345', BillingState = 'VA', BillingStreet = 'Test Street');
            insert acc;
           
            Contact Cont1 = new Contact(LastName = 'Test', FirstName = 'Contact1', AccountId = acc.Id, Email = '<EMAIL>');
            insert Cont1;

            Test.startTest();
            CampaignMember cm1 = new CampaignMember(Status = 'Registered', LeadId = L1.Id, CampaignId = c1.Id);
            CampaignMember cm3 = new CampaignMember(Status = 'Downloaded', LeadId = L1.Id, CampaignId = c2.Id);
            CampaignMember cm2 = new CampaignMember(Status = 'Attended', ContactId = Cont1.Id, CampaignId = c1.Id);
            CampaignMember cm4 = new CampaignMember(Status = 'Downloaded', ContactId = Cont1.Id, CampaignId = c2.Id);
            insert new List<CampaignMember> {
                cm1, cm2, cm3, cm4
            };
            update new CampaignMember(Id = cm1.Id, Status = 'Attended', Post_to_Chatter__c = true);
            update new CampaignMember(Id = cm2.Id, Status = 'Attended', TrialEndDate__c = system.today().addDays(10));

            delete cm4;
            Test.stopTest();
        }
    }
    
    @IsTest
    static void processChatterNotificationsTest() {
        Campaign cmp = new Campaign(Name = 'Sales Line1', IsActive = true);
        Lead ld = new Lead(LastName = 'Test', FirstName = 'Lead1', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'US', Status = 'L2 - Contacted');
        CampaignMemberTriggerHelper.InputVariables inputVariable = new CampaignMemberTriggerHelper.InputVariables(); 
        Test.startTest();
        insert cmp;
        ld.Original_Campaign_Source__c = cmp.Id;
        insert ld;
        InputVariable.leadContactId = ld.Id;
        InputVariable.status = 'MQL';
        CampaignMemberTriggerHelper.processChatterNotifications(new List<CampaignMemberTriggerHelper.InputVariables>{inputVariable});
        Test.stopTest();
    }
    
    @IsTest
    static void routingTest() {
        Campaign cmp = new Campaign(Name = 'Sales Line1', IsActive = true);
        insert cmp;
        Account acc = new Account(Name = 'Test2  Account', BillingCountry = 'United States', BillingCity = 'Test City',
                                  BillingPostalCode = '12345', BillingState = 'VA', BillingStreet = 'Test Street', AccountCategory__c = 'I1 - Integrator');
        insert acc;
        
        Test.startTest();
        Lead ld = new Lead(LastName = 'Test', FirstName = 'Lead1', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'US', Status = 'L3 - Engaged', Original_Campaign_Source__c = cmp.Id, Account_2__c = acc.Id);
        Contact cont = new Contact(LastName = 'Test', FirstName = 'Contact1', AccountId = acc.Id, Email = '<EMAIL>', L_Status__c = 'L3 - Engaged');
        insert ld;
        insert cont;   
        Test.stopTest();
    }
    
    @IsTest
    static void genericSObjectQueueableTest() {
        User marketoUser = [SELECT Id FROM User WHERE Profile.Name = 'Marketo Sync' AND IsActive = TRUE LIMIT 1];
        System.runAs(marketoUser) {
            Campaign cmp = new Campaign(Name = 'Sales Line1', IsActive = true, RecordTypeId = TriggersHelper.campaignOfferRecordTypeId);
            insert cmp;
            Account acc = new Account(Name = 'Test2  Account', BillingCountry = 'United States', BillingCity = 'Test City',
                                      BillingPostalCode = '12345', BillingState = 'VA', BillingStreet = 'Test Street', AccountCategory__c = 'P3 - Strategic Prospect');
            insert acc;
            Test.startTest();
            Lead ld = new Lead(LastName = 'Test', FirstName = 'Lead1', Company = 'Company Name', LeadSource = 'Sales Line', Country = 'US', Status = 'L3 - Engaged', Original_Campaign_Source__c = cmp.Id, Account_2__c = acc.Id);
            Contact cont = new Contact(LastName = 'Test', FirstName = 'Contact1', AccountId = acc.Id, Email = '<EMAIL>', L_Status__c = 'L3 - Engaged');
            insert ld;
            insert cont;   
            CampaignMember cm = new CampaignMember(Status = 'Registered', LeadId = ld.Id, CampaignId = cmp.Id);
            insert cm;
            PersonRouting.InputVariables inputVariable = new PersonRouting.InputVariables(); 
            InputVariable.recordId = ld.Id;
            InputVariable.status = 'L3 - Engaged';
            InputVariable.isLead = true;
            PersonRouting.personRoutingFlow(new List<PersonRouting.InputVariables>{inputVariable});
            Test.stopTest();
        }
    }
    
    @IsTest
    static void calculateOppRollupsTest() {
        Campaign cmp = new Campaign(Name = 'Sales Line Rollup', IsActive = true);
        insert cmp;
        Test.startTest();
        cmp.Recalculate__c = true;
        update cmp;
        Test.stopTest();
    }
}